import numpy as np
import shapely
import shapely.ops
import shapely.vectorized
import scipy.optimize
import scipy.special
import bpy
from mathutils import Vector
import trimesh
import time
from scipy.linalg import svd

import cma

class EllipseFitter:
    """
    A comprehensive ellipse fitting class that handles various degenerate cases
    and provides multiple fitting methods.
    """

    def __init__(self):
        self.last_error = None
        self.last_method_used = None

    def fit(self, x, y, method='auto'):
        """
        Fit an ellipse to the given points using the specified method.

        Parameters:
        -----------
        x, y : array-like
            Coordinates of the points
        method : str
            'auto' - try multiple methods automatically
            'direct' - Fitzgibbon direct least squares
            'svd' - SVD-based fitting
            'geometric' - Geometric distance minimization

        Returns:
        --------
        dict or None
            Dictionary with ellipse parameters if successful, None if failed
        """
        x = np.asarray(x, dtype=float)
        y = np.asarray(y, dtype=float)

        if len(x) != len(y):
            self.last_error = "x and y must have the same length"
            return None

        if len(x) < 5:
            self.last_error = "At least 5 points are required to fit an ellipse"
            return None

        # Check for degenerate cases
        if self._check_degenerate_cases(x, y):
            return None

        if method == 'auto':
            return self._fit_auto(x, y)
        elif method == 'direct':
            return self._fit_direct(x, y)
        elif method == 'svd':
            return self._fit_svd(x, y)
        elif method == 'geometric':
            return self._fit_geometric(x, y)
        else:
            self.last_error = f"Unknown method: {method}"
            return None

    def _check_degenerate_cases(self, x, y):
        """Check for various degenerate cases"""


        # Check for duplicate points
        unique_points = len(set(zip(x, y)))
        if unique_points < 5:
            self.last_error = f"Only {unique_points} unique points, need at least 5"


            return True

        # Check for collinearity
        if self._is_collinear(x, y):
            self.last_error = "Points are collinear"
            return True

        # Check for points forming a line segment (all points lie on a line)
        if self._points_on_line_segment(x, y):
            self.last_error = "Points lie on a line segment"
            return True

        return False

    def _is_collinear(self, x, y, tolerance=1e-10):
        """Check if points are approximately collinear"""
        if len(x) < 3:
            return True

        # Use cross product to check collinearity
        for i in range(2, len(x)):
            # Vector from point 0 to point 1
            v1 = np.array([x[1] - x[0], y[1] - y[0]])
            # Vector from point 0 to point i
            v2 = np.array([x[i] - x[0], y[i] - y[0]])
            # Cross product magnitude
            cross_product = abs(v1[0] * v2[1] - v1[1] * v2[0])
            if cross_product > tolerance:
                return False
        return True

    def _points_on_line_segment(self, x, y, tolerance=1e-8):
        """Check if all points lie approximately on a line segment"""
        if len(x) < 3:
            return True

        # Fit a line to the points and check residuals
        A = np.vstack([x, np.ones(len(x))]).T
        try:
            m, b = np.linalg.lstsq(A, y, rcond=None)[0]
            residuals = np.abs(y - (m * x + b))
            return np.all(residuals < tolerance)
        except:
            return False

    def _fit_auto(self, x, y):
        """Try multiple methods automatically"""
        methods = ['direct', 'svd', 'geometric']

        for method in methods:
            result = getattr(self, f'_fit_{method}')(x, y)
            if result is not None:
                self.last_method_used = method
                return result

        self.last_error = "All fitting methods failed"
        return None

    def _fit_direct(self, x, y):
        """Direct least squares ellipse fitting (Fitzgibbon method)"""
        # Implementation temporarily commented out
        # try:
        #     D1 = np.vstack([x**2, x*y, y**2]).T
        #     D2 = np.vstack([x, y, np.ones(len(x))]).T
        #     S1 = D1.T @ D1
        #     S2 = D1.T @ D2
        #     S3 = D2.T @ D2

        #     # Check if S3 is invertible
        #     if np.linalg.det(S3) < 1e-12:
        #         self.last_error = "S3 matrix is singular (direct method)"
        #         return None

        #     T = -np.linalg.inv(S3) @ S2.T
        #     M = S1 + S2 @ T
        #     C = np.array(((0, 0, 2), (0, -1, 0), (2, 0, 0)), dtype=float)
        #     M = np.linalg.inv(C) @ M

        #     eigval, eigvec = np.linalg.eig(M)

        #     # Find eigenvectors that satisfy the ellipse constraint
        #     con = 4 * eigvec[0] * eigvec[2] - eigvec[1]**2
        #     valid_indices = np.where(con > 0)[0]

        #     if len(valid_indices) == 0:
        #         self.last_error = "No valid ellipse solution found (direct method)"
        #         return None

        #     # Choose the eigenvector with the smallest positive eigenvalue
        #     valid_eigenvalues = eigval[valid_indices]
        #     min_idx = valid_indices[np.argmin(valid_eigenvalues)]
        #     ak = eigvec[:, min_idx]

        #     coeffs = np.concatenate((ak, T @ ak)).ravel()
        #     return self._coeffs_to_params(coeffs)

        # except Exception as e:
        #     self.last_error = f"Direct method failed: {str(e)}"
        #     return None

        self.last_error = "Direct method is temporarily disabled"
        return None

    def _fit_svd(self, x, y):
        """SVD-based ellipse fitting with cost/quality metric"""
        try:
            # Design matrix for general conic: ax^2 + bxy + cy^2 + dx + ey + f = 0
            D = np.column_stack([x**2, x*y, y**2, x, y, np.ones(len(x))])

            # SVD decomposition
            U, s, Vt = svd(D)

            # The solution is the last column of V (or last row of Vt)
            coeffs = Vt[-1, :]

            # Check if this represents an ellipse (4ac - b^2 > 0)
            a, b, c = coeffs[0], coeffs[1], coeffs[2]
            discriminant = 4*a*c - b**2

            if discriminant <= 0:
                self.last_error = "SVD solution does not represent an ellipse"
                return None

            # Calculate cost metric based on residual sum of squares
            # For each point, calculate how well it satisfies the ellipse equation
            residuals = (a * x**2 + b * x * y + c * y**2 +
                        coeffs[3] * x + coeffs[4] * y + coeffs[5])

            # Normalize residuals by the ellipse equation coefficients to get geometric meaning
            # Use the Frobenius norm of the coefficient vector for normalization
            coeff_norm = np.linalg.norm(coeffs)
            if coeff_norm > 1e-12:
                normalized_residuals = residuals / coeff_norm
            else:
                normalized_residuals = residuals

            # Cost is the root mean square of normalized residuals (lower is better)
            cost = np.sqrt(np.mean(normalized_residuals**2))

            # Get ellipse parameters
            ellipse_params = self._coeffs_to_params(coeffs)

            if ellipse_params is not None:
                # Add cost to the returned parameters
                ellipse_params['cost'] = cost
                return ellipse_params
            else:
                return None

        except Exception as e:
            self.last_error = f"SVD method failed: {str(e)}"
            return None

    def _fit_geometric(self, x, y):
        """Geometric ellipse fitting using optimization"""
        # Implementation temporarily commented out
        # try:
        #     # Initial guess
        #     x_center = np.mean(x)
        #     y_center = np.mean(y)
        #     a_init = np.std(x) * 2
        #     b_init = np.std(y) * 2
        #     theta_init = 0

        #     initial_params = [x_center, y_center, a_init, b_init, theta_init]

        #     def objective(params):
        #         x0, y0, a, b, theta = params
        #         if a <= 0 or b <= 0:
        #             return 1e10

        #         # Calculate geometric distance from each point to the ellipse
        #         total_distance = 0
        #         for xi, yi in zip(x, y):
        #             # Transform point to ellipse coordinate system
        #             cos_theta = np.cos(theta)
        #             sin_theta = np.sin(theta)
        #             dx = xi - x0
        #             dy = yi - y0
        #             x_rot = dx * cos_theta + dy * sin_theta
        #             y_rot = -dx * sin_theta + dy * cos_theta

        #             # Find closest point on ellipse (approximate)
        #             if abs(x_rot) < 1e-10 and abs(y_rot) < 1e-10:
        #                 distance = 0
        #             else:
        #                 t = np.arctan2(y_rot * a, x_rot * b)
        #                 x_ellipse = a * np.cos(t)
        #                 y_ellipse = b * np.sin(t)

        #                 # Transform back
        #                 x_closest = x0 + x_ellipse * cos_theta - y_ellipse * sin_theta
        #                 y_closest = y0 + x_ellipse * sin_theta + y_ellipse * cos_theta

        #                 distance = np.sqrt((xi - x_closest)**2 + (yi - y_closest)**2)

        #             total_distance += distance**2

        #         return total_distance

        #     # Bounds to ensure positive semi-axes
        #     bounds = [(-np.inf, np.inf), (-np.inf, np.inf), (0.01, np.inf), (0.01, np.inf), (-np.pi, np.pi)]

        #     result = minimize(objective, initial_params, bounds=bounds, method='L-BFGS-B')

        #     if result.success:
        #         x0, y0, a, b, theta = result.x
        #         # Ensure a >= b (semi-major >= semi-minor)
        #         if b > a:
        #             a, b = b, a
        #             theta += np.pi/2

        #         e = np.sqrt(1 - (b/a)**2) if a > b else 0

        #         return {
        #             'center': (x0, y0),
        #             'semi_major': a,
        #             'semi_minor': b,
        #             'eccentricity': e,
        #             'rotation': theta % np.pi,
        #             'method': 'geometric'
        #         }
        #     else:
        #         self.last_error = "Geometric optimization failed to converge"
        #         return None

        # except Exception as e:
        #     self.last_error = f"Geometric method failed: {str(e)}"
        #     return None

        self.last_error = "Geometric method is temporarily disabled"
        return None

    def _coeffs_to_params(self, coeffs):
        """Convert conic coefficients to ellipse parameters"""
        try:
            if len(coeffs) != 6:
                return None

            a, b, c, d, e, f = coeffs

            # Check if it's an ellipse
            discriminant = b**2 - 4*a*c
            if discriminant >= 0:
                self.last_error = "Coefficients do not represent an ellipse"
                return None

            # Calculate center
            den = b**2 - 4*a*c
            x0 = (2*c*d - b*e) / den
            y0 = (2*a*e - b*d) / den

            # Calculate semi-axes and rotation
            # This uses the standard formulas for conic section parameters
            theta = 0.5 * np.arctan2(b, a - c) if abs(b) > 1e-10 else 0

            # Calculate the semi-axes lengths
            cos_theta = np.cos(theta)
            sin_theta = np.sin(theta)

            # Transform to canonical form
            A = a * cos_theta**2 + b * cos_theta * sin_theta + c * sin_theta**2
            C = a * sin_theta**2 - b * cos_theta * sin_theta + c * cos_theta**2

            # Calculate the constant term in canonical form
            F = a*x0**2 + b*x0*y0 + c*y0**2 + d*x0 + e*y0 + f

            if F * A > 0 or F * C > 0:
                self.last_error = "Invalid ellipse parameters (negative semi-axes)"
                return None

            semi_major = np.sqrt(-F / A)
            semi_minor = np.sqrt(-F / C)

            # Ensure semi_major >= semi_minor
            if semi_minor > semi_major:
                semi_major, semi_minor = semi_minor, semi_major
                theta += np.pi/2

            eccentricity = np.sqrt(1 - (semi_minor/semi_major)**2) if semi_major > semi_minor else 0

            return {
                'center': (x0, y0),
                'semi_major': semi_major,
                'semi_minor': semi_minor,
                'eccentricity': eccentricity,
                'rotation': theta % np.pi,
                'coefficients': coeffs,
                'method': 'algebraic'
            }

        except Exception as e:
            self.last_error = f"Parameter conversion failed: {str(e)}"
            return None

    def get_ellipse_points(self, params, npts=100):
        """Generate points on the fitted ellipse"""
        if params is None:
            return None, None

        x0, y0 = params['center']
        a = params['semi_major']
        b = params['semi_minor']
        theta = params['rotation']

        t = np.linspace(0, 2*np.pi, npts)
        x = x0 + a * np.cos(t) * np.cos(theta) - b * np.sin(t) * np.sin(theta)
        y = y0 + a * np.cos(t) * np.sin(theta) + b * np.sin(t) * np.cos(theta)

        return x, y



def intersection_span_along_line(ellipse_data, sides_data, cutter_data, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None
        inner_correction = 0.15 # in mm
        poly = sides_data['ring']                

        outer_ellipse_geometry= create_ellipse(
            center=ellipse_data['center'],
            semi_major_axis_length=ellipse_data['major'],
            semi_minor_axis_length=ellipse_data['minor'],
            rotation=ellipse_data['rotation']
            )

        outer_linear_ring = shapely.geometry.LinearRing(outer_ellipse_geometry)                
        inter_outer = outer_linear_ring.intersection(poly)
        
        if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
            print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')           
            return None
        
        outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter_outer.geoms]
        distances.append(
            inter_outer.geoms[np.argmin(outer_projected_distances)].distance(inter_outer.geoms[np.argmax(outer_projected_distances)])
        )

        inner_ellipse_geometry = create_ellipse(
            center=ellipse_data['center'],
            semi_major_axis_length=(ellipse_data['major'] - cutter_data['radius']) + inner_correction,
            semi_minor_axis_length=(ellipse_data['minor'] - cutter_data['radius']) + inner_correction,
            rotation=ellipse_data['rotation']
            )

        inner_ellipse = shapely.geometry.LinearRing(inner_ellipse_geometry)
       
        # left half of the ellipse, ccw
        # anchor -----ccw---->end
        left_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[:36])        
        left_point = shapely.ops.nearest_points(left_inner_linear_ring, sides_data['lines'][0])[1]
        
        # reversed right half of the ellipse, cw
        # anchor -----cw---->end
        right_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[35:][::-1])
        right_point = shapely.ops.nearest_points(right_inner_linear_ring, sides_data['lines'][1])[1]
        
        distances.append(left_point.distance(right_point))
       
        front_fractions = [
            inner_ellipse.project(left_point, normalized=True),
            inner_ellipse.project(right_point, normalized=True)
            ]
        '''
        if idx==12:            
            create_line_object(sides_data['lines'][0].coords, "side_left", color=(1, 0, 0, 1))
            create_line_object(sides_data['lines'][1].coords, "side_right", color=(1, 0, 0, 1))
            create_line_object(left_inner_linear_ring.coords, "left_inner_linear_ring", color=(1, 0, 0, 1))
            create_line_object(right_inner_linear_ring.coords, "right_inner_linear_ring", color=(1, 0, 0, 1))
            create_line_object(poly.coords, "poly", color=(1, 0, 0, 1))

            create_line_object(left_point.coords, "left_point", color=(0, 1, 0, 1))
            create_line_object(right_point.coords, "right_point", color=(0, 1, 0, 1))
            for geom in inter_outer.geoms:
                create_line_object(geom.coords, "outer_inter", color=(0, 1, 0, 1))
        '''  
            

        return distances


def get_ellipse_center(anchor_point, semi_major_axis, semi_minor_axis=None, rotation=0.0):
    """Calculate the center of ellipses based on anchor points (vectorized).

    This function computes ellipse centers where the anchor point is the '3 o'clock'
    point on the ellipse's perimeter. The major axis is oriented along the global
    Y-axis and the minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (np.ndarray): Array of shape (N, 2) containing (x, y) coordinates
            that serve as the '3 o'clock' reference points on the ellipse perimeters.
        semi_major_axis (np.ndarray): 1D array of length N containing the semi-major
            axis values (the "main" radius) for each ellipse.
        semi_minor_axis (np.ndarray, optional): 1D array of length N containing the
            semi-minor axis values. If None, defaults to semi_major_axis values,
            creating circles. Defaults to None.
        rotation (np.ndarray, optional): 1D array of length N containing the geometric
            rotation of each ellipse in radians. If a scalar is provided, it will be
            broadcast to all ellipses. Defaults to 0.0.

    Returns:
        np.ndarray: Array of shape (N, 2) containing the (x, y) coordinates of the
            calculated ellipse centers.
    """
    # Convert inputs to numpy arrays for vectorized operations
    anchor_point = np.asarray(anchor_point)
    semi_major_axis = np.asarray(semi_major_axis)
    rotation = np.asarray(rotation)

    # Ensure anchor_point is 2D (N, 2)
    if anchor_point.ndim == 1:
        anchor_point = anchor_point.reshape(1, -1)

    # If semi_minor_axis is not provided, create circles by making it equal to semi_major_axis
    if semi_minor_axis is None:
        local_semi_minor = semi_major_axis
    else:
        local_semi_minor = np.asarray(semi_minor_axis)

    # Broadcast rotation to match the number of ellipses if it's a scalar
    if rotation.ndim == 0:
        rotation = np.full(len(anchor_point), rotation)

    # Calculate the offset from center to the '3 o'clock' anchor point
    # The anchor is the '3 o'clock' point. The vector from the center to this
    # point on our unrotated ellipse is (semi_minor_axis, 0). We rotate this
    # vector to find the offset from the true center to the anchor point.
    offset_x = local_semi_minor * np.cos(rotation)
    offset_y = local_semi_minor * np.sin(rotation)

    # The true center is the anchor_point minus this rotated offset vector
    center_x = anchor_point[:, 0] - offset_x
    center_y = anchor_point[:, 1] - offset_y

    # Stack the coordinates to create the final center array
    centers = np.column_stack((center_x, center_y))

    return centers


def create_ellipse_on_anchor(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=71, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.

    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def create_ellipse(center, semi_major_axis_length, resolution=71, semi_minor_axis_length=None, rotation=0.0):
    """
    Create a 2D ellipse as a numpy array with points starting from 12 o'clock position.
    
    Parameters
    ----------
    center : array_like
        The (x, y) coordinates of the ellipse center.
    semi_major_axis_length : float
        Length of the semi-major axis.
    resolution : int, optional
        Number of points to generate around the ellipse. Default is 71.
        The last point will be identical to the first point.
    semi_minor_axis_length : float, optional
        Length of the semi-minor axis. If None, defaults to semi_major_axis_length
        (creating a circle). Default is None.
    rotation : float, optional
        Rotation angle in radians. Positive values rotate counterclockwise.
        Default is 0.0.
    
    Returns
    -------
    numpy.ndarray
        A 2D array of shape (resolution, 2) containing the (x, y) coordinates
        of points on the ellipse. The first and last points are identical.
    
    Examples
    --------
    >>> # Create a circle centered at origin with radius 5
    >>> circle = create_ellipse([0, 0], 5)
    >>> circle.shape
    (71, 2)
    
    >>> # Create an ellipse with different major and minor axes
    >>> ellipse = create_ellipse([2, 3], 4, semi_minor_axis_length=2, resolution=101)
    >>> ellipse.shape
    (101, 2)
    
    >>> # Create a rotated ellipse
    >>> rotated = create_ellipse([0, 0], 3, semi_minor_axis_length=1, rotation=np.pi/4)
    """

    center = np.array(center)
    
    # Default semi_minor_axis_length to semi_major_axis_length (circle)
    if semi_minor_axis_length is None:
        semi_minor_axis_length = semi_major_axis_length
    
    # Generate angles starting from 12 o'clock (π/2) instead of 3 o'clock (0)
    # We use resolution-1 points plus duplicate the first point at the end
    angles = np.linspace(0, 2*np.pi, resolution, endpoint=False) + np.pi/2
    
    # Generate ellipse points in standard position
    x = semi_major_axis_length * np.cos(angles)
    y = semi_minor_axis_length * np.sin(angles)
    
    # Apply rotation if specified
    if rotation != 0.0:
        cos_rot = np.cos(rotation)
        sin_rot = np.sin(rotation)
        x_rot = x * cos_rot - y * sin_rot
        y_rot = x * sin_rot + y * cos_rot
        x, y = x_rot, y_rot
    
    # Translate to center
    x += center[0]
    y += center[1]
    
    # Stack points and ensure last point equals first point
    points = np.column_stack((x, y))
    
    # Make the last point identical to the first point
    points = np.vstack([points, points[0]])
    
    return points


'''
def create_ellipse(center, semi_major_axis_length, resolution=71, semi_minor_axis_length=None, rotation=0.0):
    """
    Creates a 2D ellipse centered at the given center point.

    Parameters
    ----------
    center : tuple
        The (x, y) coordinates of the ellipse center.
    semi_major_axis_length : float
        The length of the semi-major axis.
    resolution : int, optional
        The number of points to sample around the ellipse. Default is 71.
    semi_minor_axis_length : float, optional
        The length of the semi-minor axis. If None, defaults to semi_major_axis_length, creating a circle.
    rotation : float, optional
        The rotation angle in radians. Positive values rotate counter-clockwise. Default is 0.0.

    Returns
    -------
    ndarray
        A 2D numpy array of shape (resolution, 2) containing the ellipse points.
    """
    if semi_minor_axis_length is None:
        semi_minor_axis_length = semi_major_axis_length

    theta = np.linspace(0, 2 * np.pi, resolution)

    x0 = semi_minor_axis_length * np.sin(theta)
    y0 = semi_major_axis_length * np.cos(theta)

    cos_phi = np.cos(rotation)
    sin_phi = np.sin(rotation)

    x = center[0] + x0 * cos_phi - y0 * sin_phi
    y = center[1] + x0 * sin_phi + y0 * cos_phi

    return np.column_stack((x, y))

'''

def create_ellipse_old(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=71, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.

    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def find_farthest_outside_point_ng(inner_ellipse_coords: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse_coords (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.
    """
    x = inner_ellipse_coords[:, 0]
    y = inner_ellipse_coords[:, 1]

    # Filter points outside ellipse B
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b

    outside_points_geom = shapely.points(inner_ellipse_coords[outside_mask])

    # Calculate distances
    distances = shapely.distance(outer_ellipse, outside_points_geom)

    # Check if there are any outside points
    if len(distances) == 0:
        return 0.0

    # Find maximum
    return np.max(distances)


def find_t_for_arc_length_fraction(a, b, fraction):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * scipy.special.ellipe(e_sq)
    target_arc_length = fraction * circumference

    def objective_func(t):
        return a * scipy.special.ellipeinc(t, e_sq) - target_arc_length

    try:
        t_solution = scipy.optimize.brentq(objective_func, 0, 2 * np.pi)
    except ValueError:
        if np.isclose(target_arc_length, 0): t_solution = 0.0
        elif np.isclose(target_arc_length, circumference): t_solution = 2 * np.pi
        else: raise ValueError("Could not find a solution for 't'.")
    return t_solution


def rotate_vector(vector, angle_radians):
    """
    Rotates a 2D vector (as a NumPy array) by a given angle around the origin (0,0).

    Args:
        vector (np.ndarray): A 1D NumPy array representing the vector [x, y].
        angle_degrees (float): The rotation angle in degrees (counter-clockwise).

    Returns:
        np.ndarray: A 1D NumPy array representing the new vector [x_rotated, y_rotated].
    """
    c, s = np.cos(angle_radians), np.sin(angle_radians)
    rotation_matrix = np.array([[c, -s],
                                [s, c]])
    return np.dot(rotation_matrix, vector)


def get_point_and_rotation_on_ellipse_vectorized(outer_ellipse_data, fraction, normalize_normal=True):
    
    """
    Vectorized version that calculates points and outward-pointing normal vectors on an ellipse.

    Args:
        outer_ellipse_data (dict): Ellipse data containing 'minor', 'major', 'rotation', 'center'.
        fraction (np.ndarray): 1D array of fractions of the total arc length (0.0 to 1.0).
        normalize_normal (bool): If True, returns unit normal vectors.

    Returns:
        tuple: A tuple containing (point_vectors, normal_angles) where:
            - point_vectors: (N, 2) array of points
            - normal_angles: (N,) array of normal angles in radians
    """
    # Extract static ellipse parameters
    outer_semi_major_axis = outer_ellipse_data['minor']
    outer_semi_minor_axis = outer_ellipse_data['major']
    outer_rotation = outer_ellipse_data['rotation'] + np.pi/2
    normalized_rotation = outer_rotation % (2 * np.pi)
    outer_center = np.array(outer_ellipse_data['center'])

    # Vectorized arc length calculation
    t_arc = np.array([find_t_for_arc_length_fraction(outer_semi_major_axis, outer_semi_minor_axis, f) for f in fraction])
    
    # Vectorized point calculation
    cos_t, sin_t = np.cos(t_arc), np.sin(t_arc)
    point_vectors = np.column_stack([
        outer_semi_major_axis * cos_t,
        outer_semi_minor_axis * sin_t
    ])
    
    # Apply rotation
    cos_rot, sin_rot = np.cos(normalized_rotation), np.sin(normalized_rotation)
    rotated_points = np.column_stack([
        point_vectors[:, 0] * cos_rot - point_vectors[:, 1] * sin_rot,
        point_vectors[:, 0] * sin_rot + point_vectors[:, 1] * cos_rot
    ])
    point_vectors = rotated_points + outer_center

    # Vectorized normal calculation
    normal_vectors = np.column_stack([
        outer_semi_minor_axis * cos_t,
        outer_semi_major_axis * sin_t
    ])
    
    # Apply rotation to normals
    rotated_normals = np.column_stack([
        normal_vectors[:, 0] * cos_rot - normal_vectors[:, 1] * sin_rot,
        normal_vectors[:, 0] * sin_rot + normal_vectors[:, 1] * cos_rot
    ])
    
    normal_angles = np.arctan2(rotated_normals[:, 1], rotated_normals[:, 0])

    if normalize_normal:
        norms = np.linalg.norm(rotated_normals, axis=1)
        valid_mask = norms > 1e-9
        rotated_normals[valid_mask] /= norms[valid_mask, np.newaxis]
        rotated_normals[~valid_mask] = 0.0

    return point_vectors, normal_angles



def get_point_and_rotation_on_ellipse(outer_ellipse_data, fraction, normalize_normal=True):
    """
    Calculates point and its outward-pointing normal vector on an ellipse.

    Args:
        a (float): Semi-major axis.
        b (float): Semi-minor axis.
        fraction (float): Fraction of the total arc length (0.0 to 1.0).
        start_angle_rad (float): The parametric angle of the start point (fraction=0).
                                 0.0 for 3 o'clock (default).
                                 np.pi/2 for 12 o'clock.
                                 np.pi for 9 o'clock.
        normalize_normal (bool): If True, returns a unit normal vector.

    Returns:
        tuple: A tuple containing (point_vector, normal_vector).
    """
    # 1. Find the parameter 't' relative to the standard 3 o'clock start
    # This step remains the same as it correctly finds the parametric angle
    # for a given arc length fraction.
    outer_semi_major_axis = outer_ellipse_data['minor']
    outer_semi_minor_axis = outer_ellipse_data['major']
    outer_rotation = outer_ellipse_data['rotation'] + np.pi/2  #convert rotation from ellipse scpace (0,0 rad on 12 o'clock), to global rotation space (0,0 rad on 3 o'clock)
    normalized_rotation = outer_rotation % (2 * np.pi)
    outer_center = outer_ellipse_data['center']

    t_arc = find_t_for_arc_length_fraction(outer_semi_major_axis, outer_semi_minor_axis, fraction)
    
    # 3. Calculate the point vector using the standard parametric equation
    point_vector = np.array([outer_semi_major_axis * np.cos(t_arc), outer_semi_minor_axis * np.sin(t_arc)])
    point_vector = rotate_vector(point_vector, normalized_rotation)
    point_vector += outer_center

    # 4. Calculate the outward-pointing normal vector.
    # The tangent is T = [-a*sin(t), b*cos(t)].
    # The outward normal is a 90-degree clockwise rotation of the tangent: N = [b*cos(t), a*sin(t)].
    normal_vector = np.array([outer_semi_minor_axis * np.cos(t_arc), outer_semi_major_axis * np.sin(t_arc)])
    normal_vector = rotate_vector(normal_vector, normalized_rotation)
    normal_angle = np.arctan2(normal_vector[1], normal_vector[0])

    if normalize_normal:
        norm = np.linalg.norm(normal_vector)
        if norm > 1e-9: # Check for zero norm to avoid division by zero
            normal_vector /= norm
        else:
            # This case is unlikely for an ellipse but good practice
            normal_vector = np.array([0., 0.])

    return point_vector, normal_angle


def get_ellipse_distances_ng_vectorized(
    inner_ellipses: np.ndarray,
    sides_data: dict
) -> np.ndarray:
    """
    Calculates distances from an array of ellipses to a set of side geometries.

    This is a vectorized version that performs the following steps:
    1. Calculates distances between all ellipses and all sides.
    2. Checks for intersections between all ellipses and all sides.
    3. For intersecting pairs, it calculates the penetration distance relative to the outer polygon.
    4. Uses np.where to combine these results into a final distance matrix.

    Args:
        inner_ellipses (np.ndarray): A NumPy array of shapely.geometry.LineString objects.
        sides_data (dict): A dictionary containing:
            - 'lines' (list or np.ndarray): Side geometries to measure distances to.
            - 'polygon' (shapely.geometry.Polygon): The outer polygon used for penetration depth.

    Returns:
        np.ndarray: A 2D array of shape (num_ellipses, num_sides) containing the distances.
                    Distances are negative if the ellipse intersects the corresponding side line.
    """
    # Ensure inputs are NumPy arrays for broadcasting
    side_lines = np.array(sides_data['lines'])
    outer_polygon = sides_data['polygon']

    if inner_ellipses.size == 0 or side_lines.size == 0:
        return np.empty((len(inner_ellipses), len(side_lines)))

    # To compare every ellipse with every side line, we need to use broadcasting.
    # Reshape inner_ellipses to a column vector (N, 1) to operate against the
    # row vector of side_lines (M,). NumPy/Shapely will broadcast this to (N, M).
    ellipses_col = inner_ellipses[:, np.newaxis]

    # 1. Calculate the intersection mask for all (ellipse, side) pairs
    # The result is a boolean matrix of shape (num_ellipses, num_sides)
    intersects_mask = shapely.intersects(ellipses_col, side_lines)

    # 2. Calculate the "no intersection" distances for all pairs
    # This is the default distance if there's no intersection.
    # The result is a float matrix of shape (num_ellipses, num_sides)
    positive_distances = shapely.distance(ellipses_col, side_lines)

    # 3. Calculate the "intersection" distances (penetration depth)
    # This is done for each ellipse, independent of which side it hits.
    # The result is a 1D array of shape (num_ellipses,).
    penetration_distances = find_farthest_outside_points_ng_vectorized(inner_ellipses, outer_polygon)

    # Make the penetration distances negative and reshape to a column vector (N, 1)
    # so it can be broadcast across all sides for the np.where condition.
    negative_distances = -penetration_distances[:, np.newaxis]

    # 4. Combine the results using the intersection mask
    # np.where(condition, value_if_true, value_if_false)
    # If intersects_mask[i, j] is True, use the negative_distances[i].
    # Otherwise, use the positive_distances[i, j].
    final_distances = np.where(intersects_mask, negative_distances, positive_distances)

    return final_distances


def get_ellipse_distances_ng(inner_ellipse_coords: np.ndarray, inner_ellipse: shapely.geometry.LineString, sides_data):
    """
    Find new center position and rotation for an ellipse based on a new movement parameter.

    This function performs the following steps:
    1. Calculates pivot vector from outer center to inner pivot point
    2. Gets new coordinates and normal vector on the ellipse at new_move position
    3. Adjusts coordinates relative to inner pivot point
    4. Calculates angle difference between old and new pivot vectors
    5. Applies translation and rotation transformations
    6. Calculates distances to side geometries

    Args:
        ellipse (GeometryCollection): The ellipse geometry to transform
        ellipse_data (dict): Dictionary containing ellipse metrics and pivot information
        sides (MultiLineString): Side geometries to measure distances to
        new_move (float): New position parameter (0.0 to 1.0) along the ellipse

    Returns:
        tuple: (distances, angle_diff) where:
            - distances is list of distances from transformed ellipse to sides
            - angle_diff is rotation angle in radians
    """
    distances = np.zeros(2)
    for i, side_line in enumerate(sides_data['lines']):
        if inner_ellipse.intersects(side_line):
            ## ellipse part as a result of the intersection
            dist = find_farthest_outside_point_ng(inner_ellipse_coords, sides_data['polygon'])
            distances[i] = -dist # negative, because of intersection
        else:
            distances[i] = side_line.distance(inner_ellipse)

    return distances


def find_ellipse_position_ng(inner_ellipse_data, outer_ellipse_data, sides_data, front_data, search_scale, tolerance=1e-4):
    """
    Finds the optimal scale factor where the inner and outer distances are equal
    using the efficient Brent's method.
    """
    def objective_function(position):
        # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
        new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
            outer_ellipse_data = outer_ellipse_data,
            fraction=position % 1
            )

        ellipse_geometry, _ = create_ellipse(
            anchor_point=new_anchor,
            semi_major_axis=inner_ellipse_data['major'],
            semi_minor_axis=inner_ellipse_data['minor'],
            rotation=new_rotation,
            anchor_on_perimeter=True
            )

        # Calculate distances to sides
        ellipse = shapely.geometry.LineString(ellipse_geometry)

        # Calculate the distances for the given position
        distances = get_ellipse_distances_ng(ellipse_geometry, ellipse, sides_data)
        return distances[0] - distances[1]

    front_data['fractions'][0] *= search_scale
    front_data['fractions'][1] *= search_scale

    return scipy.optimize.brentq(objective_function, a=front_data['fractions'][0], b=front_data['fractions'][1], xtol=tolerance, full_output=True)


def solve_quadratic(a, b, c):
    """Solves the quadratic equation At^2 + Bt + C = 0 for t."""
    discriminant = b**2 - 4*a*c
    roots = []
    if discriminant >= 0:
        if a == 0: # Linear equation
            if b != 0:
                roots.append(-c / b)
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b + sqrt_discriminant) / (2*a)
            t2 = (-b - sqrt_discriminant) / (2*a)
            roots.append(t1)
            # Avoid adding the same root twice if discriminant is close to zero
            if abs(t1 - t2) > 1e-9:
                roots.append(t2)
    return roots


def intersect_line_ellipse(line_p1, line_p2, ellipse_data):
    """
    Finds the intersection points between a line segment and an axis-aligned ellipse.

    Args:
        line_p1 (np.ndarray): First point of the line segment (x1, y1).
        line_p2 (np.ndarray): Second point of the line segment (x2, y2).
        ellipse_center (np.ndarray): Center of the ellipse (h, k).
        semi_major_a (float): Semi-major axis length (along x).
        semi_minor_b (float): Semi-minor axis length (along y).

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
    """

    ellipse_center = ellipse_data['center']
    b = ellipse_data['major']
    a = ellipse_data['minor']

    x1, y1 = line_p1
    x2, y2 = line_p2
    h, k = ellipse_center

    # Line parameters: x(t) = x1 + t*dx, y(t) = y1 + t*dy
    dx = x2 - x1
    dy = y2 - y1

    # Vector from ellipse center to line start point
    x0 = x1 - h
    y0 = y1 - k

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # From: b^2(x0 + t*dx)^2 + a^2(y0 + t*dy)^2 = a^2*b^2
    A = b**2 * dx**2 + a**2 * dy**2
    B = 2 * (b**2 * x0 * dx + a**2 * y0 * dy)
    C = b**2 * x0**2 + a**2 * y0**2 - a**2 * b**2

    # Solve for t
    t_values = solve_quadratic(A, B, C)

    for t in t_values:
        # Check if the intersection point lies within the line segment (0 <= t <= 1)
        if 0.0 <= t <= 1.0:
            ix = x1 + t * dx
            iy = y1 + t * dy
            return np.array([ix, iy])

    return None


def get_ellipse_end_point(polygon_boundary, medial_axis_edge):
        center = shapely.get_coordinates(medial_axis_edge)[-1]
        center_point = shapely.geometry.Point(center)
        outer_semi_axis_length = center_point.distance(polygon_boundary)

        ellipse_geometry, _ = create_ellipse(
            anchor_point=center,
            semi_major_axis=outer_semi_axis_length,
            semi_minor_axis=outer_semi_axis_length,
            rotation=0.0,
            anchor_on_perimeter=False
            )

        ellipse = shapely.geometry.LinearRing(ellipse_geometry)
        if ellipse.intersects(medial_axis_edge):
            inter = ellipse.intersection(medial_axis_edge)
            if inter.geom_type == "MultiPoint":
                distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
                anchor = shapely.get_coordinates(
                    inter.geoms[np.argmax(distances)])[0]
            elif inter.geom_type == "Point":
                anchor = shapely.get_coordinates(inter)[0]
        else:
            print('ellipse does not intersect medial axis')
            return None

        # rotate 180 degrees (pi radians) around center
        return 2 * center - anchor


def get_ellipse_data(polygon_boundary, medial_axis_edge, medial_axis_start=True):
    if medial_axis_start:
        idx = 0
    else:
        idx = -1
    center = shapely.get_coordinates(medial_axis_edge)[idx]
    center_point = shapely.geometry.Point(center)
    outer_semi_axis_length = center_point.distance(polygon_boundary)
    
    ellipse_geometry = create_ellipse(
        center=center,
        semi_major_axis_length=outer_semi_axis_length        
        )
    
    ellipse = shapely.geometry.LinearRing(ellipse_geometry)
    if ellipse.intersects(medial_axis_edge):
        inter = ellipse.intersection(medial_axis_edge)
        if inter.geom_type == "MultiPoint":
            distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
            if medial_axis_start:
                idx = np.argmin(distances)                    
            else:
                idx = np.argmax(distances)                    
            anchor = shapely.get_coordinates(inter.geoms[idx])[0]
        elif inter.geom_type == "Point":
            anchor = shapely.get_coordinates(inter)[0]
        
    else:
        print('ellipse does not intersect medial axis')
        return None

    ### Initial rotation of the ellipse in radians ###
    rotation_vector = anchor - center
    # Calculate rotation (for an ellipse, so np.pi/2 is actually 0) and normalize to [-pi, pi] range
    rotation = np.arctan2(rotation_vector[1], rotation_vector[0]) - np.pi/2
    normalized_rotation = ((rotation + np.pi) % (2 * np.pi)) - np.pi

    ellipse_data = {
        'center': center,
        'major': outer_semi_axis_length,
        'minor': outer_semi_axis_length,
        'rotation': normalized_rotation,
        'roll' : False
    }    
    return ellipse_data


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects

    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry(apply_transforms: bool = False) -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []

    geometry_list = []
    for obj in selected_objects:
        if apply_transforms:
            # Apply all transforms (Location, Rotation, Scale)
            matrix = obj.matrix_world.copy()
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        if apply_transforms:
            vertices = np.array([matrix @ Vector((x, y, 0)) for x, y in vertices.reshape((-1, 3))[:, :2]])
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front2(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step

    distance_initial = np.arange(
        0.0, sampler.length, path_step)

    offsets = sampler.sample(distance_initial)
    radii = boundary_distance(polygon=polygon, points=offsets)

    return offsets, radii


def advancing_front(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step / 10.0

    distance_initial = np.arange(
        0.0, sampler.length + (path_step / 2.0), path_step)

    offset = sampler.sample(distance_initial)
    radius = boundary_distance(polygon=polygon, points=offset)

    pairs = [(offset[0], radius[0])]
    distance_result = [0]

    offsets = [offset[0]]
    radii = [radius[0]]

    for point, r, pd in zip(offset[1:],
                            radius[1:],
                            distance_initial[1:]):
        vector = point - pairs[-1][0]
        front_distance = np.linalg.norm(vector) - pairs[-1][1] + r
        if front_distance >= step:
            pairs.append((point, r))
            distance_result.append(pd)
            offsets.append(point)
            radii.append(r)

    return np.array(offsets), np.array(radii)


def create_circle_points(center, radius, start_angle, resolution=71, semi_minor=None):
    """Generate points along a circle or ellipse with given center and radius.

    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius or semi-major axis for ellipse
        start_angle: Inital rotation of the circle/ellipse in radians
        resolution: Number of points to sample on the circle/ellipse
        semi_minor: Optional semi-minor axis for ellipse. If None, creates a circle
    """
    theta = np.linspace(start_angle, start_angle + 2*np.pi, resolution)

    # If semi_minor is provided, create an ellipse, otherwise create a circle
    if semi_minor is not None:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + semi_minor * np.sin(theta)
        ])
    else:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + radius * np.sin(theta)
        ])


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def get_side_of_medial_axis(target_ls, reference_ls):
    """
    Determines on which side of a reference LineString a target LineString lies.

    Args:
        target_ls (LineString): The linestring to check.
        reference_ls (LineString): The linestring to use as the reference.

    Returns:
        int: 1 if target_ls is on one side, -1 if on the other.
             Returns 0 if the target_ls is collinear or if reference_ls
             doesn't provide enough information (e.g., is a single point).
    """
    if not isinstance(target_ls, shapely.geometry.LineString) or not isinstance(reference_ls, shapely.geometry.LineString):
        raise TypeError("Both inputs must be Shapely LineString objects.")

    if len(reference_ls.coords) < 2:
        print("Warning: Reference linestring has less than 2 points. Cannot determine side.")
        return 0

    # Get the start point of the reference linestring
    p1 = shapely.get_coordinates(reference_ls.interpolate(0.45, normalized=True)).reshape((-1))

    # Get the end point of the reference linestring
    p2 = shapely.get_coordinates(reference_ls.interpolate(0.55, normalized=True)).reshape((-1))

    # Use the first point of the target linestring as its representative point
    q = shapely.get_coordinates(target_ls.interpolate(0.5, normalized=True)).reshape((-1))

    # Calculate the orientation (2D cross product concept)
    # (p2.x - p1.x) * (q.y - p1.y) - (p2.y - p1.y) * (q.x - p1.x)
    orientation = (p2[0] - p1[0]) * (q[1] - p1[1]) - (p2[1] - p1[1]) * (q[0] - p1[0])

    # Normalize the result to -1, 0, or 1
    if orientation > 0:
        return 1  # One side
    elif orientation < 0:
        return -1 # The other side
    else:
        return 0  # Collinear


def fit_ellipse(ellipse_data, sides_data, front_data, cutter_data, min_radius_of_curvature=15.0, fitter=None, i=0):
    """
    Fit an ellipse within given constraints using optimization.

    This version cleans up diagnostics, ensures proper handling of
    intersection results, and returns updated ellipse data.
    """

    
    def _relative_side_distance(points, center, semi_major, semi_minor, angle):
        """
        Compute the relative distance to the ellipse boundary (loop over ellipses, vectorized over points)
        
        Parameters:
        - points: array-like, shape (n_points, 2)
        - centers: array-like, shape (n_ellipses, 2)
        - axes: array-like, shape (n_ellipses, 2), semi-axes [a, b] per ellipse
        - angles: array-like, shape (n_ellipses,), rotation angles in radians    
        
        Returns:
        - float array, shape (n_ellipses,): minimum distance to ellipse boundary for each ellipse
        """
        points = np.asarray(points)
        center = np.asarray(center)
        axes = np.asarray([semi_major, semi_minor])
        # print(f'axes: {axes}, points: {points}, center: {center}, angle: {angle}')

        ax = axes
        theta = angle        
        
        translated = points - center        
    
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        rot_matrix = np.array([[cos_theta, sin_theta],
                                [-sin_theta, cos_theta]])
        translated = translated @ rot_matrix.T
        
        val = (translated[:, 0] ** 2 / ax[0] ** 2) + (translated[:, 1] ** 2 / ax[1] ** 2)
        val = abs(1-np.min(val))   
        return val


    def _relative_boundary_distance(points, center, semi_major, semi_minor, angle):
        """
        Compute the relative distance to the ellipse boundary (loop over ellipses, vectorized over points)
        
        Parameters:
        - points: array-like, shape (n_points, 2)
        - centers: array-like, shape (n_ellipses, 2)
        - axes: array-like, shape (n_ellipses, 2), semi-axes [a, b] per ellipse
        - angles: array-like, shape (n_ellipses,), rotation angles in radians    
        
        Returns:
        - float array, shape (n_ellipses,): minimum distance to ellipse boundary for each ellipse
        """
        points = np.asarray(points)
        center = np.asarray(center)
        axes = np.asarray([semi_major, semi_minor])
        # print(f'axes: {axes}, points: {points}, center: {center}, angle: {angle}')

        ax = axes
        theta = angle        
        
        translated = points - center        
    
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)
        rot_matrix = np.array([[cos_theta, sin_theta],
                                [-sin_theta, cos_theta]])
        translated = translated @ rot_matrix.T
        
        val = (translated[:, 0] ** 2 / ax[0] ** 2) + (translated[:, 1] ** 2 / ax[1] ** 2)
        # print(f'val: {val}')        
        # return np.mean(np.abs(val - 1))
        return np.sum((val - 1)**2)
    

    def _get_geom_distance(line: shapely.geometry.LineString, ellipse: shapely.geometry.LinearRing):
        """
        Calculate distance from an ellipse to a line.
        
        Args:
            line: The line geometry
            ellipse: The ellipse geometry
            
        Returns:
            Distance from ellipse to line
        """
        # project_distances = [0.15, 0.3, 0.5, 0.7, 0.85]
        project_distances = [0.2, 0.35, 0.5, 0.65, 0.8]
        # scores = [0.2, 0.5, 1, 0.5, 0.2]
        # project_distances = [0.25, 0.28, 0.3, 0.4, 0.5, 0.6, 0.7, 0.72, 0.75]
        # scores = [0.1, 0.2, 0.3, 0.4, 1, 0.4, 0.3, 0.2, 0.1]
        # scores = [1, 1, 1, 1, 1, 1, 1, 1, 1]
        pts = [line.interpolate(d, normalized=True) for d in project_distances]        
        values = shapely.distance(ellipse, pts)
        return sum(values)
        # return np.sum(np.array(values) * np.array(scores))

    def _get_both_sides_distance(ellipse: shapely.geometry.LinearRing):
        """
        Calculate distance to two lines in one call and return the sum.
        More efficient than calling get_side_distance twice.
        
        Args:
            ellipse: The ellipse geometry
            
        Returns:
            Sum of distances to both lines
        """
        total_distance = 0.0
        
        # Process both lines in a single loop
        for idx in [0, 1]:
            line = sides_data['lines'][idx]
            
            if not ellipse.intersects(line):
                total_distance += ellipse.distance(line)
                continue
            
            inter = ellipse.intersection(line)
            
            if (inter.geom_type != "MultiPoint" or 
                shapely.get_num_geometries(inter) > 2):
                total_distance += 1e2 if inter.geom_type == "MultiPoint" else ellipse.distance(line)
                continue
            
            pts = list(shapely.get_parts(inter))
            
            # Calculate projections once
            line_projections = [line.project(p) for p in pts]
            ellipse_projections = [ellipse.project(p) for p in pts]
            
            # Sort indices to avoid re-projection
            line_sorted_indices = sorted(range(len(pts)), key=lambda i: line_projections[i])
            ellipse_sorted_indices = sorted(range(len(pts)), key=lambda i: ellipse_projections[i])
            
            # Create substrings
            side_part = shapely.ops.substring(
                line, 
                line_projections[line_sorted_indices[0]], 
                line_projections[line_sorted_indices[1]]
            )
            
            ellipse_part = shapely.ops.substring(
                ellipse,
                ellipse_projections[ellipse_sorted_indices[0]],
                ellipse_projections[ellipse_sorted_indices[1]]
            )
            
            total_distance += ellipse_part.hausdorff_distance(side_part)
        
        return total_distance
    
    
    def _objective_all_vectorized(params):
        position, major, minor = params

        minimum_minor = np.sqrt(min_radius_of_curvature * major)
        minor_mask = minor < minimum_minor

        # Get interpolated points along the line
        interpolated_points = front_data['line'].interpolate(position[~minor_mask], normalized=True)
        # Vectorized projection of points onto the outer ellipse
        local_pos = shapely.project(outer_ellipse, interpolated_points, normalized=True)
       
        # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
        new_anchor, new_rotation = get_point_and_rotation_on_ellipse_vectorized(
            outer_ellipse_data = ellipse_data,
            fraction=local_pos
            )
        
        center = get_ellipse_center(
            anchor_point=new_anchor,
            semi_major_axis=major,
            semi_minor_axis=minor,
            rotation=new_rotation
            )
        
        # front_distances = _get_geom_distance(front_data['line'], ellipse)
        # side_distances = _get_both_sides_distance(ellipse) * 2
        # sum_distances = front_distances + side_distances      
        
        # softness = 10
        # smooth_sum = sum_distances * (1 / softness) * scipy.special.logsumexp(softness * np.abs(sum_distances))        
        # # return sum_distances
        return 0.0

    
    def _objective_all(params):
        position, major, minor = params

        minimum_minor = np.sqrt(min_radius_of_curvature * major)
        if minor < minimum_minor:            
            return 1e6

        local_pos = outer_ellipse.project(front_data['line'].interpolate(position, normalized=True), normalized=True)
       
        # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
        new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
            outer_ellipse_data = ellipse_data,
            fraction=local_pos
            )
        
        center  = create_ellipse_on_anchor(
            anchor_point=new_anchor,
            semi_major_axis=major,
            semi_minor_axis=minor,
            rotation=new_rotation,
            anchor_on_perimeter=True,
            only_center=True
            )
        
        project_distances = np.linspace(0.2, 0.8, 5)
        pts = [front_data['line'].interpolate(d, normalized=True).coords[0] for d in project_distances]
        
        distances_front = _relative_boundary_distance(pts, center, major, minor, new_rotation+np.pi/2)

        l_l = shapely.segmentize(sides_data['lines'][0], 0.1)        
        pts = list(l_l.coords)
        distances_side = _relative_side_distance(pts, center, major, minor, new_rotation+np.pi/2) ** 2
        
        l_r = shapely.segmentize(sides_data['lines'][1], 0.1)
        pts = list(l_r.coords)        
        distances_side += _relative_side_distance(pts, center, major, minor, new_rotation+np.pi/2) ** 2
                
        distances = distances_front + distances_side
        # softness = 10
        # distances = distances * (1 / softness) * scipy.special.logsumexp(softness * np.abs(distances))        
        return distances  

    distances = intersection_span_along_line(ellipse_data, sides_data, cutter_data)

    ## 0- outer, 1- inner
    # print(f'distances o:{ distances[0]/2}, i:{ distances[1]/2} ')
    if distances[0] >= distances[1]:
        # print('bigger than previous')
        minimum_major = distances[1]
        maximum_major = distances[0]
    else:
        # print('smaller than previous')
        minimum_major = distances[0]
        maximum_major = distances[1]

    min_max_ratio = minimum_major / maximum_major

    if min_max_ratio > 0.8: ## TO-DO: make it dynamic / try optimization
        min_max_correction = 0.25 * min_max_ratio
    else:
        min_max_correction = 0.0

    # print(f'min_max_ratio: {min_max_ratio}, min_max_correction: {min_max_correction}')

    minimum_major /= 2 + min_max_correction
    maximum_major /= 2 - min_max_correction

    minimum_minor = np.sqrt(min_radius_of_curvature * maximum_major)
    bnds = scipy.optimize.Bounds(
        [0.1, minimum_major, minimum_minor],  # Lower bounds for position, major, minor
        [0.8, maximum_major, maximum_major]   # Upper bounds
    )

    # print(f'i {i}, minimum_major: {minimum_major}, maximum_major: {maximum_major}')

    outer_ellipse_geometry = create_ellipse(
            center=ellipse_data['center'],
            semi_major_axis_length=ellipse_data['major'],
            semi_minor_axis_length=ellipse_data['minor'],
            rotation=ellipse_data['rotation']
            )
    
    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)
    ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)
    shapely.prepare(ellipse)

    # CMA-ES needs an initial starting point (x0) and a step size (sigma0)
    # A good starting point is often the center of the bounds.
    initial_guess = [0.5, minimum_major, minimum_minor]
    
    # The initial step size is crucial. A good rule of thumb is 1/4 to 1/3 of the search range.
    sigma0 = 0.25 

    bounds_list = [
        [0.2, minimum_major, minimum_minor],  # Lower bounds for position, major, minor
        [0.8, maximum_major, maximum_major]   # Upper bounds
    ]

    opts = {
        'popsize': 20,        
        'bounds': bounds_list,
        'tolfun': 5e-2,
        'tolx': 6e-2,
        'tolstagnation': 10,
        # 'verb_disp': False
    }
    
    # es, best = cma.fmin2(
    #     _objective_all,
    #     initial_guess,
    #     sigma0,
    #     options=opts
    #     )    
    # position, major, minor = es
    
    # result = scipy.optimize.minimize(
    #     _objective_all, 
    #     x0=[0.5, minimum_major, minimum_minor],        
    #     method = 'SLSQP',        
    #     bounds=bnds,        
    #     tol=5e-6,        
    #     # options={'disp': False, 'ftol': 5e-6},
    # )
    # print(f'result relative: {result}')
    # position, major, minor = result.x    
    time1 = time.time()
    result = scipy.optimize.differential_evolution(
            _objective_all,
            bounds=bnds,
            init='sobol',
            seed=42,  # For reproducibility
            popsize=20,  # Higher for better exploration in 3D (try 15-30)
            tol=2e-5,  # Tight tolerance to aim for res ~0
            maxiter=1,
            # mutation=(0.5, 1.0), 
            # recombination=0.7,
            strategy='rand1bin',  # Robust for non-smooth functions
            # strategy='best2exp',  # Robust for non-smooth functions
            # workers= -1
        )
    time2 = time.time()
    print(f'Time: {time2 - time1:.2f}s')
    print(f'result: {result}')
    position, major, minor = result.x    
    if False:
    # if result.fun > 1.0:
        print(f'i: {i}, cost too high.')
        es, _ = cma.fmin2(
            _objective_all,
            initial_guess,
            sigma0,            
            options=opts
        )    
        position, major, minor = es

        # result = scipy.optimize.differential_evolution(
        #     _objective_all,
        #     bounds=bnds,
        #     # init=initial_guess,  # Good for bounded spaces; or use your initial as a custom init
        #     seed=42,  # For reproducibility
        #     popsize=10,  # Higher for better exploration in 3D (try 15-30)
        #     tol=2e-5,  # Tight tolerance to aim for res ~0
        #     maxiter=20,
        #     # mutation=(0.5, 1.0), 
        #     # recombination=0.7,
        #     # strategy='rand1bin',  # Robust for non-smooth functions
        #     strategy='best1bin',  # Robust for non-smooth functions
        #     # workers= -1
        # )

    major_range = (major - minimum_major) / (maximum_major - minimum_major)
    # print(f'major (0-1): {major_range:.3f}')       
    
    local_pos = outer_ellipse.project(front_data['line'].interpolate(position, normalized=True), normalized=True)    
    
    # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
    new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
        outer_ellipse_data = ellipse_data,
        fraction=local_pos
        )
    
    _, new_center = create_ellipse_on_anchor(
        anchor_point=new_anchor,
        semi_major_axis=major,
        semi_minor_axis=minor,
        rotation=new_rotation,
        anchor_on_perimeter=True
        )
    
    # Update ellipse data with optimization results
    ellipse_data.update({
        'center': new_center,
        'major': major+cutter_data['radius'],
        'minor': minor+cutter_data['radius'],        
        'rotation': new_rotation-np.pi/2,
    })
    return ellipse_data


def get_split_coords(ellipse_data):
    cx, cy = ellipse_data['center']
    angle_rad = ellipse_data['rotation'] - np.pi/2
    x = cx + ellipse_data['major'] * np.cos(angle_rad)
    y = cy + ellipse_data['major'] * np.sin(angle_rad)
    return np.array([x, y])


def resample_linestring(line: shapely.geometry.LineString, segment_length: float) -> shapely.geometry.LineString:
    """
    Resamples a Shapely LineString to have segments of a constant length.

    The last segment will be shorter than segment_length if the total
    length is not a multiple of segment_length.

    Args:
        line (LineString): The original LineString to resample.
        segment_length (float): The desired length of each segment.

    Returns:
        LineString: The new, resampled LineString.
    """
    if segment_length <= 0:
        raise ValueError("Segment length must be positive.")

    # Get the total length of the original line
    total_length = line.length

    # Generate distances along the line at which to place new vertices
    # np.arange creates a sequence from 0 to total_length with a step of segment_length
    distances = np.arange(0, total_length, segment_length)

    # Interpolate points at these distances
    # The list comprehension is a concise way to do this
    new_points = [line.interpolate(dist) for dist in distances]

    # Always include the very last point of the original line to ensure
    # the resampled line has the same extent.
    # We can access the last coordinate directly.
    last_point = shapely.geometry.Point(line.coords[-1])
    if not new_points[-1].equals(last_point):
         new_points.append(last_point)


    # Create a new LineString from the list of points
    return shapely.geometry.LineString(new_points)


def get_init_helper_geoms(ellipse_data, sides_data, cutter_data):
    outer_ellipse_geometry = create_ellipse(
        center=ellipse_data['center'],
        semi_major_axis_length=ellipse_data['major'],
        semi_minor_axis_length=ellipse_data['minor'],
        rotation=ellipse_data['rotation']
    )

    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)
    outer_ellipse_polygon = shapely.geometry.Polygon(outer_ellipse_geometry)
    shapely.prepare(outer_ellipse)
    shapely.prepare(outer_ellipse_polygon)

    inter_outer = outer_ellipse.intersection(sides_data['ring'])    

    if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
        print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
        return None

    outer_projected_distances = [outer_ellipse.project(d, normalized=True) for d in inter_outer.geoms]

    sides_fractions = [
        sides_data['lines'][0].project(inter_outer.geoms[np.argmin(outer_projected_distances)], normalized=True),
        sides_data['lines'][1].project(inter_outer.geoms[np.argmax(outer_projected_distances)], normalized=True)
    ]    
    
    front_fractions = [min(outer_projected_distances), -(1-max(outer_projected_distances))] #front fractions range: -/0-1
    front_line_part0 = shapely.ops.substring(outer_ellipse, 0, min(outer_projected_distances), normalized=True)
    front_line_part1 = shapely.ops.substring(outer_ellipse, max(outer_projected_distances), 1, normalized=True)
    front_line = shapely.ops.linemerge([front_line_part0, front_line_part1])
    
    front_data = {
        'line': front_line,        
        'fractions': front_fractions,
        'sides_fractions': sides_fractions        
    }

    ## works only on the first iteration. TO-DO: move from loop.
    ellipse_geometry = create_ellipse(
        center=ellipse_data['center'],
        semi_major_axis_length=(ellipse_data['major'] - cutter_data['radius'])+0.25,        
        semi_minor_axis_length=(ellipse_data['minor'] - cutter_data['radius'])+0.25,
        rotation=ellipse_data['rotation']
    )
    if ellipse_data['roll']:
        ellipse_geometry = np.roll(ellipse_geometry, int(len(ellipse_geometry)/2), axis=0)

    ellipse = shapely.geometry.LinearRing(ellipse_geometry)    
  
    sides_fractions_inner = []
    for side in sides_data['lines']:
        inter = ellipse.intersection(side)
        if inter.geom_type == "Point":
            sides_fractions_inner.append(side.project(inter, normalized=True))       
        else:
            print(f'ellipse intersection with side is not a point: {inter.geom_type}')            

    sides_parts = []
    for inner, outer, side in zip(
            sides_fractions_inner,
            front_data['sides_fractions'],
            sides_data['lines']
        ):
        sides_parts.append(shapely.ops.substring(side, inner, outer, normalized=True))

    opposite_lines = [
        shapely.geometry.LineString([outer_ellipse_geometry[0], outer_ellipse_geometry[29]]), #left
        shapely.geometry.LineString([outer_ellipse_geometry[0], outer_ellipse_geometry[43]]) #right        
    ]

    helper_geoms = {
        'outer_ellipse': outer_ellipse,
        'outer_ellipse_polygon': outer_ellipse_polygon,
        'front_data': front_data,
        'sides_parts': sides_parts,
        'opposite_lines': opposite_lines
    }
    if inter.geom_type == "MultiPoint":
        for part in helper_geoms['sides_parts']:
            create_line_object(part.coords, "side_part", color=(1, 0, 0, 1))

    return front_data


def get_front_data(ellipse_data, sides_data):    
    #outer part
    outer_ellipse_geometry = create_ellipse(
        center=ellipse_data['center'],
        semi_major_axis_length=ellipse_data['major'],
        semi_minor_axis_length=ellipse_data['minor'],
        rotation=ellipse_data['rotation']
    )
    
    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)    
    outer_ellipse = shapely.remove_repeated_points(outer_ellipse)        
    shapely.prepare(outer_ellipse)    

    inter_outer = outer_ellipse.intersection(sides_data['ring'])

    if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
        print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
        return None    
    
    outer_projected_distances = [outer_ellipse.project(d, normalized=True) for d in inter_outer.geoms]

    front_fractions = [min(outer_projected_distances), -(1-max(outer_projected_distances))] #front fractions range: -/0-1
    front_line_part0 = shapely.ops.substring(outer_ellipse, 0, min(outer_projected_distances), normalized=True)
    front_line_part1 = shapely.ops.substring(outer_ellipse, max(outer_projected_distances), 1, normalized=True)
    front_line = shapely.ops.linemerge([front_line_part0, front_line_part1])
    
    front_data = {
        'line': front_line,        
        'fractions': front_fractions
    }

    return front_data


def get_sides_and_ellipse_data(base_geoms):
    ## Sides per path
    trochos_offsets, trochos_radii = advancing_front(base_geoms['medial_axis_coords'], base_geoms['polygon_buffered'], 1) # 5mm between trochoids
    
    points_array = shapely.points(trochos_offsets)
    buffered_points_array = shapely.buffer(points_array, trochos_radii, quad_segs=18) #about 72 points per circle
    buffered_points_array[0] = shapely.buffer(points_array[0], trochos_radii[0], quad_segs=36)
    buffered_points_array[-1] = shapely.buffer(points_array[-1], trochos_radii[-1], quad_segs=36)
    
    trochos_poly = shapely.unary_union(buffered_points_array).simplify(0.05)
    shapely.prepare(trochos_poly)
    trochos_poly_coords = shapely.get_coordinates(trochos_poly.exterior)
    
    boundary = base_geoms['polygon'].boundary
    
    ##ellipse data
    ellipse_data = get_ellipse_data(boundary, base_geoms['medial_axis'], medial_axis_start=True)    
    end_ellipse_data = get_ellipse_data(boundary, base_geoms['medial_axis'], medial_axis_start=False)
    
    start_split_coords = get_split_coords(ellipse_data)
    end_split_coords = get_split_coords(end_ellipse_data)
    distances =scipy.spatial.distance.cdist([start_split_coords, end_split_coords], trochos_poly_coords)  # Shape: (len(P), len(points))
    split0, split1 = np.argmin(distances, axis=1)
    
    # Ensure proper ordering
    start, end = min(split0, split1), max(split0, split1)

    # Create two continuous segments
    side0 = shapely.geometry.LineString(trochos_poly_coords[start:end+1]).simplify(0.001)
    side1_coords = np.concatenate((trochos_poly_coords[end:], trochos_poly_coords[:start+1]))
    side1 = shapely.geometry.LineString(side1_coords).simplify(0.001)
    sides = [side0, side1]
    # sides = [resample_linestring(side, 0.1) for side in sides]

    ### Determine sides, the first side is the one that is on the "left" of the medial axis
    if get_side_of_medial_axis(sides[0], base_geoms['medial_axis']) == -1:
        sides.reverse()

    # reverse right side for match direction with left: left -->, right -->
    sides[1] = sides[1].reverse()

    for side in sides:
        shapely.prepare(side)

    trochos_ring = shapely.geometry.LinearRing(trochos_poly.exterior.coords)
    shapely.prepare(trochos_ring)

    sides_data = {
        'ring': trochos_ring,
        'polygon': trochos_poly,
        'lines': sides
    }
    return sides_data, ellipse_data


def main():
    cutter_dim = 10 # in mm

    cutter_data = {
        'diameter': cutter_dim,
        'radius': cutter_dim / 2
    }

    # Get geometry and validate
    geometry = get_geometry(apply_transforms=True)

    biggest_gap = None
    biggest_gap_idx = None

    # Check distance between first and last point of each geometry, to find the medial axis.
    for idx, geom in enumerate(geometry[1:]):
        if len(geom) >= 2:  # Ensure geometry has at least 2 points
            start_point = geom[0]
            end_point = geom[-1]
            distance = np.linalg.norm(np.array(end_point) - np.array(start_point))

            if biggest_gap is None or distance > biggest_gap:
                biggest_gap = distance
                biggest_gap_idx = idx+1 # +1 because we skip the first geometry

    #reorder geometry so that the medial axis is the second element
    if biggest_gap_idx is not None:
        geometry = [geometry[0]] + [geometry[biggest_gap_idx]] + geometry[1:biggest_gap_idx] + geometry[biggest_gap_idx+1:]

    if len(geometry) < 3:
        print('Please select at least three objects.')
    else:
        holes = [geom for geom in geometry[2:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes).normalize() ## reorder rings if they are not in the expected orientation (exterior ccw, interiors cw)
        medial_axis = shapely.geometry.LineString(geometry[1])

    polygon_buffered = polygon.buffer(-cutter_data['radius'])
    medial_middle = medial_axis.interpolate(0.5, normalized=True)

    all_geoms = [shapely.geometry.LineString(polygon_buffered.exterior.coords)] + \
                 [shapely.geometry.LineString(interior.coords) for interior in polygon_buffered.interiors]

    tolerance = 0.03
    distances = []
    for line in all_geoms:
        distance = medial_middle.distance(line)
        distances.append(distance)

    active_geoms = [line for line, distance in zip(all_geoms, distances) if abs(distance - min(distances)) < tolerance]
    
    base_geoms = {
        'polygon': polygon,
        'polygon_buffered': polygon_buffered,
        'medial_axis': medial_axis,
        'medial_axis_coords': geometry[1] # medial axis coords in original shape
    }
   
    sides_data, ellipse_data = get_sides_and_ellipse_data(base_geoms)
    front_data = get_init_helper_geoms(ellipse_data, sides_data, cutter_data)

    create_line_object(sides_data['lines'][0].coords, "side_left", color=(0, 1, 0, 1))
    create_line_object(sides_data['lines'][1].coords, "side_right", color=(1, 0, 0, 1))   
    
    min_radius_of_curvature = 10.0    
    fitter = EllipseFitter()

    ellipse_geometry = create_ellipse(
            center=ellipse_data['center'],
            semi_major_axis_length=ellipse_data['major']-cutter_data['radius'],
            semi_minor_axis_length=ellipse_data['minor']-cutter_data['radius'],
            rotation=ellipse_data['rotation']
            )
        
    time1 = time.time()
    for i in range(1):        
        create_line_object(front_data['line'].coords, "front_line", color=(0, 1, 0, 1))
        ellipse_data = fit_ellipse(ellipse_data, sides_data, front_data, cutter_data, min_radius_of_curvature, fitter, i)        
        ellipse_geometry = create_ellipse(
            center=ellipse_data['center'],
            semi_major_axis_length=ellipse_data['major']-cutter_data['radius'],
            semi_minor_axis_length=ellipse_data['minor']-cutter_data['radius'],
            rotation=ellipse_data['rotation']
            )
        
        # ell_part0 = ellipse_geometry[0:20]
        # ell_part1 = ellipse_geometry[-20:]
        # ell_merged = np.concatenate((ell_part1, ell_part0))
        # create_line_object(ell_merged, "ellipse_inner", color=(0.2, 0.2, 0.8, 1))
        create_line_object(ellipse_geometry, "ellipse_inner", color=(0.2, 0.2, 0.8, 1))
        front_data = get_front_data(ellipse_data, sides_data)
    time2 = time.time()
    print(f'Time: {time2-time1}')

if __name__ == "__main__":
    main()
